// 3D太空射击游戏

// 游戏状态
const GameState = {
    MENU: 'menu',
    PLAYING: 'playing',
    GAME_OVER: 'game_over',
    PAUSED: 'paused'
};

// 3D游戏类
class Game3D {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.state = GameState.MENU;
        
        // 游戏数据
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // Three.js 核心组件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.clock = new THREE.Clock();
        
        // 游戏对象
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        this.particles = [];
        
        // 游戏设置
        this.enemySpawnRate = 0.02;
        this.lastEnemySpawn = 0;
        
        // 输入处理
        this.keys = {};
        this.mouse = { x: 0, y: 0 };
        
        this.init3D();
        this.setupEventListeners();
        this.gameLoop();
    }
    
    init3D() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000428);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75, 
            this.canvas.width / this.canvas.height, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: this.canvas,
            antialias: true 
        });
        this.renderer.setSize(this.canvas.width, this.canvas.height);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 添加光照
        this.setupLighting();
        
        // 创建星空背景
        this.createStarField();
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
        
        // 主光源
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // 点光源（用于特效）
        const pointLight = new THREE.PointLight(0x00ffff, 0.5, 100);
        pointLight.position.set(0, 10, 0);
        this.scene.add(pointLight);
    }
    
    createStarField() {
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 1000;
        const positions = new Float32Array(starCount * 3);
        
        for (let i = 0; i < starCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 200;     // x
            positions[i + 1] = (Math.random() - 0.5) * 200; // y
            positions[i + 2] = (Math.random() - 0.5) * 200; // z
        }
        
        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const starMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.5,
            transparent: true,
            opacity: 0.8
        });
        
        const stars = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(stars);
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            
            if (e.code === 'Space') {
                e.preventDefault();
                if (this.state === GameState.PLAYING && this.player) {
                    this.player.shoot();
                }
            }
            
            if (e.code === 'KeyP') {
                this.togglePause();
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // 鼠标事件
        document.addEventListener('mousemove', (e) => {
            if (this.state === GameState.PLAYING) {
                this.mouse.x = (e.clientX / window.innerWidth) * 2 - 1;
                this.mouse.y = -(e.clientY / window.innerHeight) * 2 + 1;
            }
        });
        
        // 按钮事件
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.startGame();
        });
    }
    
    startGame() {
        this.state = GameState.PLAYING;
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // 清空场景中的游戏对象
        this.clearGameObjects();
        
        // 创建3D玩家
        this.player = new Player3D(this.scene);
        
        // 清空数组
        this.bullets = [];
        this.enemies = [];
        this.particles = [];
        
        // 隐藏菜单
        document.getElementById('gameOverlay').style.display = 'none';
        
        this.updateUI();
    }
    
    clearGameObjects() {
        // 移除所有游戏对象（保留光照和星空）
        const objectsToRemove = [];
        this.scene.traverse((child) => {
            if (child.userData.isGameObject) {
                objectsToRemove.push(child);
            }
        });
        
        objectsToRemove.forEach(obj => {
            this.scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) obj.material.dispose();
        });
    }
    
    togglePause() {
        if (this.state === GameState.PLAYING) {
            this.state = GameState.PAUSED;
        } else if (this.state === GameState.PAUSED) {
            this.state = GameState.PLAYING;
        }
    }
    
    gameOver() {
        this.state = GameState.GAME_OVER;
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('startMenu').style.display = 'none';
        document.getElementById('gameOverMenu').style.display = 'block';
        document.getElementById('gameOverlay').style.display = 'flex';
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
    }
    
    update() {
        if (this.state !== GameState.PLAYING) return;
        
        const deltaTime = this.clock.getDelta();
        
        // 更新玩家
        if (this.player) {
            this.player.update(this.keys, this.mouse, deltaTime);
        }
        
        // 更新子弹
        this.bullets = this.bullets.filter(bullet => {
            bullet.update(deltaTime);
            if (bullet.position.z < -50) {
                this.scene.remove(bullet.mesh);
                return false;
            }
            return true;
        });
        
        // 更新敌人
        this.enemies = this.enemies.filter(enemy => {
            enemy.update(deltaTime);
            if (enemy.position.z > 20) {
                this.scene.remove(enemy.mesh);
                return false;
            }
            return true;
        });
        
        // 生成敌人
        if (Math.random() < this.enemySpawnRate) {
            this.spawnEnemy();
        }
        
        // 碰撞检测
        this.checkCollisions();
        
        // 更新相机跟随
        this.updateCamera();
    }
    
    spawnEnemy() {
        const x = (Math.random() - 0.5) * 20;
        const y = (Math.random() - 0.5) * 10;
        const enemy = new Enemy3D(this.scene, x, y, -30);
        this.enemies.push(enemy);
    }
    
    updateCamera() {
        if (this.player) {
            // 相机跟随玩家，但保持一定距离
            const targetX = this.player.position.x * 0.3;
            const targetY = this.player.position.y * 0.3 + 5;
            
            this.camera.position.x += (targetX - this.camera.position.x) * 0.05;
            this.camera.position.y += (targetY - this.camera.position.y) * 0.05;
            
            // 相机始终看向前方
            this.camera.lookAt(this.player.position.x * 0.2, this.player.position.y * 0.2, -10);
        }
    }
    
    checkCollisions() {
        // 子弹击中敌人
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            for (let j = this.enemies.length - 1; j >= 0; j--) {
                if (this.bullets[i] && this.enemies[j]) {
                    const distance = this.bullets[i].position.distanceTo(this.enemies[j].position);
                    if (distance < 2) {
                        // 创建爆炸效果
                        this.createExplosion(this.enemies[j].position);
                        
                        // 移除对象
                        this.scene.remove(this.bullets[i].mesh);
                        this.scene.remove(this.enemies[j].mesh);
                        this.bullets.splice(i, 1);
                        this.enemies.splice(j, 1);
                        
                        // 增加分数
                        this.score += 10;
                        this.updateUI();
                        break;
                    }
                }
            }
        }
        
        // 敌人撞击玩家
        if (this.player) {
            for (let i = this.enemies.length - 1; i >= 0; i--) {
                const distance = this.player.position.distanceTo(this.enemies[i].position);
                if (distance < 3) {
                    this.createExplosion(this.enemies[i].position);
                    this.scene.remove(this.enemies[i].mesh);
                    this.enemies.splice(i, 1);
                    this.lives--;
                    this.updateUI();
                    
                    if (this.lives <= 0) {
                        this.gameOver();
                    }
                }
            }
        }
    }
    
    createExplosion(position) {
        // 创建简单的爆炸粒子效果
        const particleCount = 20;
        const geometry = new THREE.SphereGeometry(0.1, 8, 8);
        const material = new THREE.MeshBasicMaterial({ color: 0xff4444 });
        
        for (let i = 0; i < particleCount; i++) {
            const particle = new THREE.Mesh(geometry, material);
            particle.position.copy(position);
            particle.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10
            );
            particle.userData.life = 1.0;
            particle.userData.isGameObject = true;
            this.scene.add(particle);
            this.particles.push(particle);
        }
    }
    
    render() {
        // 更新粒子
        this.particles = this.particles.filter(particle => {
            particle.position.add(particle.userData.velocity.clone().multiplyScalar(0.016));
            particle.userData.life -= 0.02;
            particle.material.opacity = particle.userData.life;
            
            if (particle.userData.life <= 0) {
                this.scene.remove(particle);
                return false;
            }
            return true;
        });
        
        // 渲染场景
        this.renderer.render(this.scene, this.camera);
        
        // 绘制暂停提示
        if (this.state === GameState.PAUSED) {
            const ctx = this.canvas.getContext('2d');
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            
            ctx.fillStyle = '#00ffff';
            ctx.font = '48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂停', this.canvas.width / 2, this.canvas.height / 2);
        }
    }
    
    gameLoop() {
        this.update();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// 3D玩家类
class Player3D {
    constructor(scene) {
        this.scene = scene;
        this.position = new THREE.Vector3(0, 0, 0);
        this.speed = 15;
        this.shootCooldown = 0;

        this.createMesh();
    }

    createMesh() {
        // 创建飞船几何体
        const geometry = new THREE.ConeGeometry(1, 3, 8);
        const material = new THREE.MeshLambertMaterial({ color: 0x00ffff });

        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.copy(this.position);
        this.mesh.rotation.x = Math.PI; // 让锥形指向前方
        this.mesh.castShadow = true;
        this.mesh.userData.isGameObject = true;

        // 添加引擎火焰效果
        const flameGeometry = new THREE.CylinderGeometry(0.2, 0.5, 1, 8);
        const flameMaterial = new THREE.MeshBasicMaterial({
            color: 0xff4444,
            transparent: true,
            opacity: 0.8
        });

        this.flame1 = new THREE.Mesh(flameGeometry, flameMaterial);
        this.flame1.position.set(-0.5, 0, 2);
        this.flame2 = new THREE.Mesh(flameGeometry, flameMaterial);
        this.flame2.position.set(0.5, 0, 2);

        this.mesh.add(this.flame1);
        this.mesh.add(this.flame2);

        this.scene.add(this.mesh);
    }

    update(keys, mouse, deltaTime) {
        // 移动控制
        const moveSpeed = this.speed * deltaTime;

        if ((keys['KeyA'] || keys['ArrowLeft']) && this.position.x > -15) {
            this.position.x -= moveSpeed;
        }
        if ((keys['KeyD'] || keys['ArrowRight']) && this.position.x < 15) {
            this.position.x += moveSpeed;
        }
        if ((keys['KeyW'] || keys['ArrowUp']) && this.position.y < 10) {
            this.position.y += moveSpeed;
        }
        if ((keys['KeyS'] || keys['ArrowDown']) && this.position.y > -5) {
            this.position.y -= moveSpeed;
        }

        // 更新网格位置
        this.mesh.position.copy(this.position);

        // 根据鼠标位置轻微倾斜飞船
        this.mesh.rotation.z = -mouse.x * 0.3;
        this.mesh.rotation.y = mouse.x * 0.2;

        // 引擎火焰动画
        const flameScale = 1 + Math.sin(Date.now() * 0.01) * 0.3;
        this.flame1.scale.y = flameScale;
        this.flame2.scale.y = flameScale;

        // 射击冷却
        if (this.shootCooldown > 0) {
            this.shootCooldown -= deltaTime;
        }
    }

    shoot() {
        if (this.shootCooldown <= 0) {
            const bullet = new Bullet3D(this.scene, this.position.x, this.position.y, this.position.z - 1);
            game3d.bullets.push(bullet);
            this.shootCooldown = 0.2;
        }
    }
}

// 3D敌人类
class Enemy3D {
    constructor(scene, x, y, z) {
        this.scene = scene;
        this.position = new THREE.Vector3(x, y, z);
        this.speed = 8;

        this.createMesh();
    }

    createMesh() {
        // 创建敌人飞船
        const geometry = new THREE.BoxGeometry(2, 1, 3);
        const material = new THREE.MeshLambertMaterial({ color: 0xff4444 });

        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.copy(this.position);
        this.mesh.castShadow = true;
        this.mesh.userData.isGameObject = true;

        // 添加翅膀
        const wingGeometry = new THREE.BoxGeometry(4, 0.2, 1);
        const wingMaterial = new THREE.MeshLambertMaterial({ color: 0x880000 });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.position.set(0, 0, 0);
        this.mesh.add(wings);

        this.scene.add(this.mesh);
    }

    update(deltaTime) {
        this.position.z += this.speed * deltaTime;
        this.mesh.position.copy(this.position);

        // 旋转动画
        this.mesh.rotation.y += deltaTime * 2;
    }
}

// 3D子弹类
class Bullet3D {
    constructor(scene, x, y, z) {
        this.scene = scene;
        this.position = new THREE.Vector3(x, y, z);
        this.speed = 30;

        this.createMesh();
    }

    createMesh() {
        const geometry = new THREE.CylinderGeometry(0.1, 0.1, 1, 8);
        const material = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            emissive: 0xffff00,
            emissiveIntensity: 0.3
        });

        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.copy(this.position);
        this.mesh.rotation.x = Math.PI / 2;
        this.mesh.userData.isGameObject = true;

        this.scene.add(this.mesh);
    }

    update(deltaTime) {
        this.position.z -= this.speed * deltaTime;
        this.mesh.position.copy(this.position);
    }
}

// 启动3D游戏
const game3d = new Game3D();
