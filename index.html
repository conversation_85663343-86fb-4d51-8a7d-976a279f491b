<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D太空射击游戏</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <div class="score">分数: <span id="score">0</span></div>
            <div class="lives">生命: <span id="lives">3</span></div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="game-overlay" id="gameOverlay">
            <div class="menu" id="startMenu">
                <h1>3D太空射击</h1>
                <p>使用 WASD 或方向键移动</p>
                <p>按空格键射击</p>
                <p>鼠标移动控制视角</p>
                <button id="startBtn">开始游戏</button>
            </div>
            
            <div class="menu" id="gameOverMenu" style="display: none;">
                <h1>游戏结束</h1>
                <p>最终分数: <span id="finalScore">0</span></p>
                <button id="restartBtn">重新开始</button>
            </div>
        </div>
    </div>
    
    <div class="controls">
        <h3>3D游戏控制:</h3>
        <p>移动: WASD 键或方向键</p>
        <p>射击: 空格键</p>
        <p>视角: 鼠标移动</p>
        <p>暂停: P 键</p>
    </div>
    
    <script src="game.js"></script>
</body>
</html>
