* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.game-container {
    position: relative;
    background: #000;
    border: 3px solid #00ffff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.game-header {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    background: rgba(0, 255, 255, 0.1);
    border-bottom: 2px solid #00ffff;
    font-size: 18px;
    font-weight: bold;
}

#gameCanvas {
    display: block;
    background: linear-gradient(180deg, #000428 0%, #004e92 100%);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu {
    text-align: center;
    background: rgba(0, 20, 40, 0.9);
    padding: 40px;
    border-radius: 15px;
    border: 2px solid #00ffff;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
}

.menu h1 {
    font-size: 3em;
    margin-bottom: 20px;
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.menu p {
    font-size: 1.2em;
    margin: 10px 0;
    color: #ccc;
}

button {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2em;
    font-weight: bold;
    border-radius: 25px;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 255, 0.5);
    background: linear-gradient(45deg, #0080ff, #00ffff);
}

button:active {
    transform: translateY(0);
}

.controls {
    margin-top: 20px;
    text-align: center;
    background: rgba(0, 20, 40, 0.7);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #00ffff;
}

.controls h3 {
    color: #00ffff;
    margin-bottom: 10px;
}

.controls p {
    margin: 5px 0;
    color: #ccc;
}

/* 响应式设计 */
@media (max-width: 900px) {
    #gameCanvas {
        width: 100%;
        height: auto;
    }
    
    .game-container {
        width: 100%;
        max-width: 800px;
    }
    
    .menu h1 {
        font-size: 2em;
    }
    
    .menu {
        padding: 20px;
    }
}
