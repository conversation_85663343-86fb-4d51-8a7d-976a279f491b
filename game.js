// 游戏状态
const GameState = {
    MENU: 'menu',
    PLAYING: 'playing',
    GAME_OVER: 'game_over',
    PAUSED: 'paused'
};

// 游戏类
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.state = GameState.MENU;
        
        // 游戏数据
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // 游戏对象
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        this.particles = [];
        
        // 游戏设置
        this.enemySpawnRate = 0.02;
        this.lastEnemySpawn = 0;
        
        // 输入处理
        this.keys = {};
        this.setupEventListeners();
        
        // 开始游戏循环
        this.gameLoop();
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            
            if (e.code === 'Space') {
                e.preventDefault();
                if (this.state === GameState.PLAYING) {
                    this.player.shoot();
                }
            }
            
            if (e.code === 'KeyP') {
                this.togglePause();
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // 按钮事件
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startGame();
        });
        
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.startGame();
        });
    }
    
    startGame() {
        this.state = GameState.PLAYING;
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // 创建玩家
        this.player = new Player(this.canvas.width / 2, this.canvas.height - 50);
        
        // 清空数组
        this.bullets = [];
        this.enemies = [];
        this.particles = [];
        
        // 隐藏菜单
        document.getElementById('gameOverlay').style.display = 'none';
        
        this.updateUI();
    }
    
    togglePause() {
        if (this.state === GameState.PLAYING) {
            this.state = GameState.PAUSED;
        } else if (this.state === GameState.PAUSED) {
            this.state = GameState.PLAYING;
        }
    }
    
    gameOver() {
        this.state = GameState.GAME_OVER;
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('startMenu').style.display = 'none';
        document.getElementById('gameOverMenu').style.display = 'block';
        document.getElementById('gameOverlay').style.display = 'flex';
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
    }
    
    update() {
        if (this.state !== GameState.PLAYING) return;
        
        // 更新玩家
        if (this.player) {
            this.player.update(this.keys);
        }
        
        // 更新子弹
        this.bullets = this.bullets.filter(bullet => {
            bullet.update();
            return bullet.y > -10;
        });
        
        // 更新敌人
        this.enemies = this.enemies.filter(enemy => {
            enemy.update();
            return enemy.y < this.canvas.height + 50;
        });
        
        // 生成敌人
        if (Math.random() < this.enemySpawnRate) {
            this.spawnEnemy();
        }
        
        // 碰撞检测
        this.checkCollisions();
        
        // 更新粒子效果
        this.particles = this.particles.filter(particle => {
            particle.update();
            return particle.life > 0;
        });
    }
    
    spawnEnemy() {
        const x = Math.random() * (this.canvas.width - 40);
        const enemy = new Enemy(x, -30);
        this.enemies.push(enemy);
    }
    
    checkCollisions() {
        // 子弹击中敌人
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            for (let j = this.enemies.length - 1; j >= 0; j--) {
                if (this.bullets[i] && this.enemies[j] && 
                    this.checkCollision(this.bullets[i], this.enemies[j])) {
                    
                    // 创建爆炸效果
                    this.createExplosion(this.enemies[j].x, this.enemies[j].y);
                    
                    // 移除子弹和敌人
                    this.bullets.splice(i, 1);
                    this.enemies.splice(j, 1);
                    
                    // 增加分数
                    this.score += 10;
                    this.updateUI();
                    break;
                }
            }
        }
        
        // 敌人撞击玩家
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            if (this.player && this.checkCollision(this.player, this.enemies[i])) {
                this.createExplosion(this.enemies[i].x, this.enemies[i].y);
                this.enemies.splice(i, 1);
                this.lives--;
                this.updateUI();
                
                if (this.lives <= 0) {
                    this.gameOver();
                }
            }
        }
    }
    
    checkCollision(obj1, obj2) {
        return obj1.x < obj2.x + obj2.width &&
               obj1.x + obj1.width > obj2.x &&
               obj1.y < obj2.y + obj2.height &&
               obj1.y + obj1.height > obj2.y;
    }
    
    createExplosion(x, y) {
        for (let i = 0; i < 8; i++) {
            const particle = new Particle(x, y);
            this.particles.push(particle);
        }
    }
    
    render() {
        // 清空画布
        this.ctx.fillStyle = 'rgba(0, 4, 40, 0.1)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制星空背景
        this.drawStars();
        
        if (this.state === GameState.PLAYING || this.state === GameState.PAUSED) {
            // 绘制游戏对象
            if (this.player) this.player.render(this.ctx);
            
            this.bullets.forEach(bullet => bullet.render(this.ctx));
            this.enemies.forEach(enemy => enemy.render(this.ctx));
            this.particles.forEach(particle => particle.render(this.ctx));
            
            // 绘制暂停提示
            if (this.state === GameState.PAUSED) {
                this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                this.ctx.fillStyle = '#00ffff';
                this.ctx.font = '48px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('暂停', this.canvas.width / 2, this.canvas.height / 2);
            }
        }
    }
    
    drawStars() {
        this.ctx.fillStyle = 'white';
        for (let i = 0; i < 50; i++) {
            const x = (i * 37) % this.canvas.width;
            const y = (i * 73) % this.canvas.height;
            const size = Math.sin(Date.now() * 0.001 + i) * 0.5 + 1;
            this.ctx.fillRect(x, y, size, size);
        }
    }
    
    gameLoop() {
        this.update();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// 玩家类
class Player {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 40;
        this.speed = 5;
        this.shootCooldown = 0;
    }

    update(keys) {
        // 移动控制
        if ((keys['KeyA'] || keys['ArrowLeft']) && this.x > 0) {
            this.x -= this.speed;
        }
        if ((keys['KeyD'] || keys['ArrowRight']) && this.x < 800 - this.width) {
            this.x += this.speed;
        }
        if ((keys['KeyW'] || keys['ArrowUp']) && this.y > 0) {
            this.y -= this.speed;
        }
        if ((keys['KeyS'] || keys['ArrowDown']) && this.y < 600 - this.height) {
            this.y += this.speed;
        }

        // 射击冷却
        if (this.shootCooldown > 0) {
            this.shootCooldown--;
        }
    }

    shoot() {
        if (this.shootCooldown <= 0) {
            const bullet = new Bullet(this.x + this.width / 2 - 2, this.y, -8);
            game.bullets.push(bullet);
            this.shootCooldown = 10;
        }
    }

    render(ctx) {
        // 绘制飞船
        ctx.fillStyle = '#00ffff';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 绘制飞船细节
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(this.x + 5, this.y + 5, 30, 10);
        ctx.fillRect(this.x + 15, this.y + 20, 10, 15);

        // 绘制引擎火焰
        ctx.fillStyle = '#ff4444';
        ctx.fillRect(this.x + 10, this.y + 40, 8, 10);
        ctx.fillRect(this.x + 22, this.y + 40, 8, 10);
    }
}

// 敌人类
class Enemy {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 30;
        this.height = 30;
        this.speed = 2 + Math.random() * 2;
    }

    update() {
        this.y += this.speed;
    }

    render(ctx) {
        // 绘制敌人飞船
        ctx.fillStyle = '#ff4444';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 绘制敌人细节
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(this.x + 5, this.y + 20, 20, 5);
        ctx.fillRect(this.x + 12, this.y + 5, 6, 15);
    }
}

// 子弹类
class Bullet {
    constructor(x, y, speedY) {
        this.x = x;
        this.y = y;
        this.width = 4;
        this.height = 10;
        this.speedY = speedY;
    }

    update() {
        this.y += this.speedY;
    }

    render(ctx) {
        ctx.fillStyle = '#ffff00';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 添加发光效果
        ctx.shadowColor = '#ffff00';
        ctx.shadowBlur = 10;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        ctx.shadowBlur = 0;
    }
}

// 粒子类（爆炸效果）
class Particle {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.vx = (Math.random() - 0.5) * 8;
        this.vy = (Math.random() - 0.5) * 8;
        this.life = 30;
        this.maxLife = 30;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.life--;
    }

    render(ctx) {
        const alpha = this.life / this.maxLife;
        ctx.fillStyle = `rgba(255, ${Math.floor(100 + alpha * 155)}, 0, ${alpha})`;
        ctx.fillRect(this.x, this.y, 3, 3);
    }
}

// 启动游戏
const game = new Game();
